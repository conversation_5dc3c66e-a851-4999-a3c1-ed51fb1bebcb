using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class RestoreSchedulesCommandHandler : IRestoreSchedulesCommandHandler
{
    private readonly ILogger<RestoreSchedulesCommandHandler> _logger;
    private readonly IScheduleManagementService _scheduleManagementService;
    private readonly IScrapingConfigurationService _configurationService;

    public RestoreSchedulesCommandHandler(
        ILogger<RestoreSchedulesCommandHandler> logger,
        IScheduleManagementService scheduleManagementService,
        IScrapingConfigurationService configurationService)
    {
        _logger = logger;
        _scheduleManagementService = scheduleManagementService;
        _configurationService = configurationService;
    }

    public async Task<RestoreSchedulesResult> Consume(RestoreSchedulesCommand command, CancellationToken cancellationToken = default)
    {
        var result = new RestoreSchedulesResult();

        try
        {
            _logger.LogInformation("Starting restoration of Apify schedules for existing journals");

            result.JournalsProcessed = command.Journals.Count;

            var journalGroups = command.Journals
                .GroupBy(j => j.CronExpression)
                .ToList();

            var webhookUrl = _configurationService.GetWebhookUrl();
            if (string.IsNullOrEmpty(webhookUrl))
            {
                result.Errors.Add("Webhook URL not configured");
                return result;
            }

            foreach (var group in journalGroups)
            {
                try
                {
                    await ProcessScheduleGroup(group, webhookUrl, result, cancellationToken);
                }
                catch (Exception ex)
                {
                    var errorMessage = $"Error processing schedule group with cron '{group.Key}': {ex.Message}";
                    _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
                    result.Errors.Add(errorMessage);
                }
            }

            _logger.LogInformation("Completed restoration. Journals: {Journals}, Schedules: {Schedules}, Tasks: {Tasks}, Webhooks: {Webhooks}, Errors: {Errors}",
                result.JournalsProcessed, result.SchedulesCreated, result.TasksCreated, result.WebhooksCreated, result.Errors.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, "Failed to restore schedules");
            result.Errors.Add($"Failed to restore schedules: {ex.Message}");
            return result;
        }
    }

    private async Task ProcessScheduleGroup(
        IGrouping<string, JournalScheduleInfo> group,
        string webhookUrl,
        RestoreSchedulesResult result,
        CancellationToken cancellationToken)
    {
        var cronExpression = group.Key;
        var journals = group.ToList();

        try
        {
            var scheduleResult = await _scheduleManagementService.CreateOrUpdateGroupScheduleAsync(
                journals, cronExpression, webhookUrl, cancellationToken);

            if (!string.IsNullOrEmpty(scheduleResult.ErrorMessage))
            {
                _logger.LogWarning(LoggingConstants.ScheduleRestorationErrorTemplate, scheduleResult.ErrorMessage);
                result.Errors.Add(scheduleResult.ErrorMessage);
                return;
            }

            if (scheduleResult.TaskCreated)
            {
                result.TasksCreated++;
                result.Messages.Add($"Created new group task for {journals.Count} journals with cron '{cronExpression}'");
            }
            else if (scheduleResult.TaskUpdated)
            {
                result.Messages.Add($"Updated existing task for {journals.Count} journals with cron '{cronExpression}'");
            }

            if (scheduleResult.ScheduleCreated)
            {
                result.SchedulesCreated++;
                result.Messages.Add($"Created new schedule for cron '{cronExpression}' with {journals.Count} journals");
            }
            else
            {
                result.Messages.Add($"Reused existing schedule for cron '{cronExpression}' with {journals.Count} journals");
            }

            if (scheduleResult.WebhookCreated)
            {
                result.WebhooksCreated++;
                result.Messages.Add($"Created webhook for task '{scheduleResult.TaskId}' covering {journals.Count} journals");
            }

            _logger.LogInformation("Successfully processed schedule group for cron '{CronExpression}' with {JournalCount} journals - Task: {TaskId} (Created: {TaskCreated}, Updated: {TaskUpdated})",
                cronExpression, journals.Count, scheduleResult.TaskId, scheduleResult.TaskCreated, scheduleResult.TaskUpdated);
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to process schedule group for cron '{cronExpression}': {ex.Message}";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
            result.Errors.Add(errorMessage);
        }
    }
}
