﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services
{
    public class CreateOrUpdateScheduleCommandHandler : ICreateOrUpdateScheduleCommandHandler
    {
        private readonly ILogger<CreateOrUpdateScheduleCommandHandler> _logger;
        private readonly IScheduleManagementService _scheduleManagementService;
        private readonly IScrapingConfigurationService _configurationService;

        public CreateOrUpdateScheduleCommandHandler(
            ILogger<CreateOrUpdateScheduleCommandHandler> logger,
            IScheduleManagementService scheduleManagementService,
            IScrapingConfigurationService configurationService)
        {
            _logger = logger;
            _scheduleManagementService = scheduleManagementService;
            _configurationService = configurationService;
        }

        public async Task Consume(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("CreateOrUpdateScheduleCommandHandler:Consume:{Journal}", LogSanitizer.Sanitize(command.NewJournal.Name));
            try
            {
                if (command.OperationType == JournalOperationType.Create)
                {
                    await OnJournalAddedAsync(command, cancellationToken);
                }
                else if (command.OperationType == JournalOperationType.Update)
                {
                    await OnJournalUpdatedAsync(command, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Failed to create or update Apify schedule: {ex.Message}";
                _logger.LogError(ex, LoggingConstants.CreateOrUpdateScheduleErrorTemplate, errorMessage);
                throw new InvalidOperationException(errorMessage, ex);
            }

            _logger.LogInformation("CreateOrUpdateJournalCommandHandler:Consume: Completed.");
        }

        private async Task OnJournalAddedAsync(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken = default)
        {
            var webhookUrl = _configurationService.GetWebhookUrl();
            if (string.IsNullOrEmpty(webhookUrl))
            {
                var errorMessage = $"Failed to create schedule for journal '{command.NewJournal.Name}': Webhook URL is missing";
                _logger.LogWarning(LoggingConstants.CreateOrUpdateScheduleErrorTemplate, errorMessage);
                throw new InvalidOperationException(errorMessage);
            }

            var journal = new JournalScheduleInfo
            {
                Id = command.NewJournal.Id,
                Name = command.NewJournal.Name,
                Url = command.NewJournal.Url,
                CronExpression = command.NewJournal.CronExpression
            };

            var result = await _scheduleManagementService.CreateOrUpdateJournalScheduleAsync(journal, webhookUrl, cancellationToken);

            if (!string.IsNullOrEmpty(result.ErrorMessage))
            {
                _logger.LogError(LoggingConstants.CreateOrUpdateScheduleErrorTemplate, result.ErrorMessage);
                throw new InvalidOperationException(result.ErrorMessage);
            }

            await UpdateJournalWithTaskIdAsync(command, result.TaskId);
        }

        private async Task OnJournalUpdatedAsync(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken = default)
        {
            var webhookUrl = _configurationService.GetWebhookUrl();
            if (string.IsNullOrEmpty(webhookUrl))
            {
                var errorMessage = $"Failed to update schedule for journal '{command.NewJournal.Name}': Webhook URL is missing";
                _logger.LogWarning(LoggingConstants.CreateOrUpdateScheduleErrorTemplate, errorMessage);
                throw new InvalidOperationException(errorMessage);
            }

            string? taskId = null;

            if (command.NewJournal.CronExpression != command.OldJournal!.CronExpression)
            {
                if (!string.IsNullOrEmpty(command.OldJournal.TaskId))
                {
                    await RemoveUrlFromOldTask(command, cancellationToken);
                }

                var journal = new JournalScheduleInfo
                {
                    Id = command.NewJournal.Id,
                    Name = command.NewJournal.Name,
                    Url = command.NewJournal.Url,
                    CronExpression = command.NewJournal.CronExpression
                };

                var result = await _scheduleManagementService.CreateOrUpdateJournalScheduleAsync(journal, webhookUrl, cancellationToken);

                if (!string.IsNullOrEmpty(result.ErrorMessage))
                {
                    _logger.LogError(LoggingConstants.CreateOrUpdateScheduleErrorTemplate, result.ErrorMessage);
                    throw new InvalidOperationException(result.ErrorMessage);
                }

                taskId = result.TaskId;
            }
            else if (command.NewJournal.Url != command.OldJournal.Url)
            {
                taskId = await UpdateUrlInExistingTask(command, cancellationToken);
            }

            await UpdateJournalWithTaskIdAsync(command, taskId);
        }

        private async Task RemoveUrlFromOldTask(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken)
        {
            try
            {
                var existingSchedule = await _scheduleManagementService.FindExistingScheduleAsync(command.OldJournal!.CronExpression, cancellationToken);
                if (existingSchedule != null && !string.IsNullOrEmpty(command.OldJournal.TaskId))
                {
                    _logger.LogInformation("Removing URL '{OldUrl}' from task '{TaskId}' for old cron expression '{OldCron}'",
                        command.OldJournal.Url, command.OldJournal.TaskId, command.OldJournal.CronExpression);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to remove URL from old task for journal '{JournalName}'", command.NewJournal.Name);
            }
        }

        private async Task<string?> UpdateUrlInExistingTask(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken)
        {
            try
            {
                var existingSchedule = await _scheduleManagementService.FindExistingScheduleAsync(command.NewJournal.CronExpression, cancellationToken);
                if (existingSchedule != null)
                {
                    var taskId = await _scheduleManagementService.UpdateExistingTaskAsync(existingSchedule, command.NewJournal.Url, cancellationToken);
                    if (!string.IsNullOrEmpty(taskId))
                    {
                        return taskId;
                    }
                }

                var webhookUrl = _configurationService.GetWebhookUrl();
                var journal = new JournalScheduleInfo
                {
                    Id = command.NewJournal.Id,
                    Name = command.NewJournal.Name,
                    Url = command.NewJournal.Url,
                    CronExpression = command.NewJournal.CronExpression
                };

                var result = await _scheduleManagementService.CreateOrUpdateJournalScheduleAsync(journal, webhookUrl, cancellationToken);

                if (!string.IsNullOrEmpty(result.ErrorMessage))
                {
                    throw new InvalidOperationException(result.ErrorMessage);
                }

                return result.TaskId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update URL in existing task for journal '{JournalName}'", command.NewJournal.Name);
                throw;
            }
        }



        private static async Task UpdateJournalWithTaskIdAsync(CreateOrUpdateScheduleCommand command, string? taskId)
        {
            if (command.UpdateJournalWithTaskId != null && taskId != null)
            {
                await command.UpdateJournalWithTaskId(taskId, command.NewJournal.Id);
            }
        }
    }
}
