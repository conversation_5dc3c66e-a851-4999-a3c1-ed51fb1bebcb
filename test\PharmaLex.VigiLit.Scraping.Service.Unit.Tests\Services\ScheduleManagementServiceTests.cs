using Apify.SDK.Model;
using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Services;
using PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Builders;
using Task = System.Threading.Tasks.Task;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Services;

public class ScheduleManagementServiceTests
{
    private readonly Mock<ILogger<ScheduleManagementService>> _loggerMock;
    private readonly Mock<IApifyScheduleService> _scheduleServiceMock;
    private readonly Mock<IApifyTaskService> _taskServiceMock;
    private readonly Mock<IApifyWebhookService> _webhookServiceMock;
    private readonly ScheduleManagementService _sut;

    private const string TestWebhookUrl = "https://example.com/webhook";
    private const string TestTaskId = "test-task-123";
    private const string TestScheduleId = "test-schedule-456";
    private const string TestCronExpression = "0 9 * * 1";

    public ScheduleManagementServiceTests()
    {
        _loggerMock = new Mock<ILogger<ScheduleManagementService>>();
        _scheduleServiceMock = new Mock<IApifyScheduleService>();
        _taskServiceMock = new Mock<IApifyTaskService>();
        _webhookServiceMock = new Mock<IApifyWebhookService>();

        _sut = new ScheduleManagementService(
            _scheduleServiceMock.Object,
            _taskServiceMock.Object,
            _webhookServiceMock.Object,
            _loggerMock.Object);
    }

    [Fact]
    public async Task FindExistingScheduleAsync_WhenScheduleExists_ReturnsSchedule()
    {
        // Arrange
        var existingSchedule = new GetListOfSchedulesResponseDataItems(
            id: TestScheduleId,
            userId: "test-user",
            name: "test-schedule",
            cronExpression: TestCronExpression,
            timezone: "UTC",
            isEnabled: true,
            isExclusive: false,
            createdAt: DateTime.UtcNow.ToString("O"),
            modifiedAt: DateTime.UtcNow.ToString("O"),
            nextRunAt: DateTime.UtcNow.AddHours(1).ToString("O"),
            lastRunAt: string.Empty,
            actions: new List<GetListOfSchedulesResponseDataItemsActions>());

        var schedulesResponse = new GetListOfSchedulesResponse(
            new GetListOfSchedulesResponseData(1, 1, 0, false, 0, new List<GetListOfSchedulesResponseDataItems> { existingSchedule }));

        _scheduleServiceMock.Setup(x => x.GetSchedulesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(schedulesResponse);

        // Act
        var result = await _sut.FindExistingScheduleAsync(TestCronExpression);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(TestScheduleId, result.Id);
        Assert.Equal(TestCronExpression, result.CronExpression);
    }

    [Fact]
    public async Task FindExistingScheduleAsync_WhenScheduleDoesNotExist_ReturnsNull()
    {
        // Arrange
        var schedulesResponse = new GetListOfSchedulesResponse(
            new GetListOfSchedulesResponseData(0, 0, 0, false, 0, new List<GetListOfSchedulesResponseDataItems>()));

        _scheduleServiceMock.Setup(x => x.GetSchedulesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(schedulesResponse);

        // Act
        var result = await _sut.FindExistingScheduleAsync(TestCronExpression);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task CreateOrUpdateGroupScheduleAsync_WhenNoExistingSchedule_CreatesNewResources()
    {
        // Arrange
        var journals = new List<JournalScheduleInfo>
        {
            new JournalScheduleInfoBuilder().WithName("Journal 1").WithCronExpression(TestCronExpression).WithUrl("https://journal1.com").Build(),
            new JournalScheduleInfoBuilder().WithName("Journal 2").WithCronExpression(TestCronExpression).WithUrl("https://journal2.com").Build()
        };

        var schedulesResponse = new GetListOfSchedulesResponse(
            new GetListOfSchedulesResponseData(0, 0, 0, false, 0, new List<GetListOfSchedulesResponseDataItems>()));

        _scheduleServiceMock.Setup(x => x.GetSchedulesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(schedulesResponse);

        _taskServiceMock.Setup(x => x.CreateGroupTaskAsync(It.IsAny<IEnumerable<JournalScheduleInfo>>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(TestTaskId);

        _scheduleServiceMock.Setup(x => x.CreateScheduleForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _webhookServiceMock.Setup(x => x.CreateWebhookForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _sut.CreateOrUpdateGroupScheduleAsync(journals, TestCronExpression, TestWebhookUrl);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(TestTaskId, result.TaskId);
        Assert.True(result.TaskCreated);
        Assert.True(result.ScheduleCreated);
        Assert.True(result.WebhookCreated);
        Assert.False(result.TaskUpdated);
        Assert.Null(result.ErrorMessage);

        _taskServiceMock.Verify(x => x.CreateGroupTaskAsync(
            It.Is<IEnumerable<JournalScheduleInfo>>(j => j.Count() == 2), 
            It.IsAny<string>(), 
            It.IsAny<CancellationToken>()), Times.Once);
        _scheduleServiceMock.Verify(x => x.CreateScheduleForTaskAsync(TestTaskId, It.IsAny<string>(), TestCronExpression, It.IsAny<CancellationToken>()), Times.Once);
        _webhookServiceMock.Verify(x => x.CreateWebhookForTaskAsync(TestTaskId, TestWebhookUrl, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CreateOrUpdateGroupScheduleAsync_WhenExistingScheduleExists_UpdatesExistingTask()
    {
        // Arrange
        var journals = new List<JournalScheduleInfo>
        {
            new JournalScheduleInfoBuilder().WithName("Journal 1").WithCronExpression(TestCronExpression).WithUrl("https://journal1.com").Build()
        };

        var existingSchedule = new GetListOfSchedulesResponseDataItems(
            id: TestScheduleId,
            userId: "test-user",
            name: "test-schedule",
            cronExpression: TestCronExpression,
            timezone: "UTC",
            isEnabled: true,
            isExclusive: false,
            createdAt: DateTime.UtcNow.ToString("O"),
            modifiedAt: DateTime.UtcNow.ToString("O"),
            nextRunAt: DateTime.UtcNow.AddHours(1).ToString("O"),
            lastRunAt: string.Empty,
            actions: new List<GetListOfSchedulesResponseDataItemsActions>());

        var schedulesResponse = new GetListOfSchedulesResponse(
            new GetListOfSchedulesResponseData(1, 1, 0, false, 0, new List<GetListOfSchedulesResponseDataItems> { existingSchedule }));

        _scheduleServiceMock.Setup(x => x.GetSchedulesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(schedulesResponse);

        _taskServiceMock.Setup(x => x.GetTaskIdsByScheduleIdAsync(TestScheduleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<string> { TestTaskId });

        _taskServiceMock.Setup(x => x.UpdateTaskStartUrlsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _sut.CreateOrUpdateGroupScheduleAsync(journals, TestCronExpression, TestWebhookUrl);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(TestTaskId, result.TaskId);
        Assert.Equal(TestScheduleId, result.ScheduleId);
        Assert.False(result.TaskCreated);
        Assert.False(result.ScheduleCreated);
        Assert.False(result.WebhookCreated);
        Assert.True(result.TaskUpdated);
        Assert.Null(result.ErrorMessage);

        _taskServiceMock.Verify(x => x.UpdateTaskStartUrlsAsync(TestTaskId, TestScheduleId, "https://journal1.com", null, It.IsAny<CancellationToken>()), Times.Once);
        _taskServiceMock.Verify(x => x.CreateGroupTaskAsync(It.IsAny<IEnumerable<JournalScheduleInfo>>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task CreateOrUpdateJournalScheduleAsync_WhenSuccessful_ReturnsResult()
    {
        // Arrange
        var journal = new JournalScheduleInfoBuilder()
            .WithName("Test Journal")
            .WithCronExpression(TestCronExpression)
            .WithUrl("https://test.com")
            .Build();

        var schedulesResponse = new GetListOfSchedulesResponse(
            new GetListOfSchedulesResponseData(0, 0, 0, false, 0, new List<GetListOfSchedulesResponseDataItems>()));

        _scheduleServiceMock.Setup(x => x.GetSchedulesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(schedulesResponse);

        _taskServiceMock.Setup(x => x.CreateTaskForJournalAsync(It.IsAny<JournalScheduleInfo>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(TestTaskId);

        _scheduleServiceMock.Setup(x => x.CreateScheduleForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _webhookServiceMock.Setup(x => x.CreateWebhookForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _sut.CreateOrUpdateJournalScheduleAsync(journal, TestWebhookUrl);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(TestTaskId, result.TaskId);
        Assert.True(result.TaskCreated);
        Assert.True(result.ScheduleCreated);
        Assert.True(result.WebhookCreated);
        Assert.Null(result.ErrorMessage);
    }

    [Fact]
    public async Task UpdateExistingTaskAsync_WhenTaskExists_UpdatesTaskAndReturnsTaskId()
    {
        // Arrange
        var schedule = new GetListOfSchedulesResponseDataItems(
            id: TestScheduleId,
            userId: "test-user",
            name: "test-schedule",
            cronExpression: TestCronExpression,
            timezone: "UTC",
            isEnabled: true,
            isExclusive: false,
            createdAt: DateTime.UtcNow.ToString("O"),
            modifiedAt: DateTime.UtcNow.ToString("O"),
            nextRunAt: DateTime.UtcNow.AddHours(1).ToString("O"),
            lastRunAt: string.Empty,
            actions: new List<GetListOfSchedulesResponseDataItemsActions>());

        _taskServiceMock.Setup(x => x.GetTaskIdsByScheduleIdAsync(TestScheduleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<string> { TestTaskId });

        _taskServiceMock.Setup(x => x.UpdateTaskStartUrlsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _sut.UpdateExistingTaskAsync(schedule, "https://new-url.com");

        // Assert
        Assert.Equal(TestTaskId, result);
        _taskServiceMock.Verify(x => x.UpdateTaskStartUrlsAsync(TestTaskId, TestScheduleId, "https://new-url.com", null, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateExistingTaskAsync_WhenNoTaskExists_ReturnsNull()
    {
        // Arrange
        var schedule = new GetListOfSchedulesResponseDataItems(
            id: TestScheduleId,
            userId: "test-user",
            name: "test-schedule",
            cronExpression: TestCronExpression,
            timezone: "UTC",
            isEnabled: true,
            isExclusive: false,
            createdAt: DateTime.UtcNow.ToString("O"),
            modifiedAt: DateTime.UtcNow.ToString("O"),
            nextRunAt: DateTime.UtcNow.AddHours(1).ToString("O"),
            lastRunAt: string.Empty,
            actions: new List<GetListOfSchedulesResponseDataItemsActions>());

        _taskServiceMock.Setup(x => x.GetTaskIdsByScheduleIdAsync(TestScheduleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<string>());

        // Act
        var result = await _sut.UpdateExistingTaskAsync(schedule, "https://new-url.com");

        // Assert
        Assert.Null(result);
    }
}
