using Apify.SDK.Model;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Helpers;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class ScheduleManagementService : IScheduleManagementService
{
    private readonly IApifyScheduleService _scheduleService;
    private readonly IApifyTaskService _taskService;
    private readonly IApifyWebhookService _webhookService;
    private readonly ILogger<ScheduleManagementService> _logger;

    public ScheduleManagementService(
        IApifyScheduleService scheduleService,
        IApifyTaskService taskService,
        IApifyWebhookService webhookService,
        ILogger<ScheduleManagementService> logger)
    {
        _scheduleService = scheduleService;
        _taskService = taskService;
        _webhookService = webhookService;
        _logger = logger;
    }

    public async Task<GetListOfSchedulesResponseDataItems?> FindExistingScheduleAsync(string cronExpression, CancellationToken cancellationToken = default)
    {
        try
        {
            var schedules = await _scheduleService.GetSchedulesAsync(cancellationToken);
            var existingSchedule = schedules?.Data.Items.Find(x => x.CronExpression == cronExpression);
            
            if (existingSchedule != null)
            {
                _logger.LogInformation("Found existing schedule '{ScheduleId}' for cron expression '{CronExpression}'", 
                    existingSchedule.Id, cronExpression);
            }
            else
            {
                _logger.LogInformation("No existing schedule found for cron expression '{CronExpression}'", cronExpression);
            }

            return existingSchedule;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finding existing schedule for cron expression '{CronExpression}'", cronExpression);
            throw;
        }
    }

    public async Task<ScheduleManagementResult> CreateOrUpdateGroupScheduleAsync(
        IEnumerable<JournalScheduleInfo> journals, 
        string cronExpression, 
        string webhookUrl, 
        CancellationToken cancellationToken = default)
    {
        var result = new ScheduleManagementResult();
        var journalList = journals.ToList();

        try
        {
            var existingSchedule = await FindExistingScheduleAsync(cronExpression, cancellationToken);
            
            if (existingSchedule != null)
            {
                // Try to update existing task with new URLs
                var taskId = await UpdateExistingTaskWithMultipleUrls(existingSchedule, journalList, cancellationToken);
                if (!string.IsNullOrEmpty(taskId))
                {
                    result.TaskId = taskId;
                    result.ScheduleId = existingSchedule.Id;
                    result.TaskUpdated = true;
                    _logger.LogInformation("Updated existing task '{TaskId}' for schedule '{ScheduleId}' with {UrlCount} URLs", 
                        taskId, existingSchedule.Id, journalList.Count);
                    return result;
                }
            }

            // Create new group task, schedule, and webhook
            var groupTaskName = ScrapingHelper.GenerateUniqueName("group-task", cronExpression);
            var groupScheduleName = ScrapingHelper.GenerateUniqueName("schedule", cronExpression);

            result.TaskId = await _taskService.CreateGroupTaskAsync(journalList, groupTaskName, cancellationToken);
            if (string.IsNullOrEmpty(result.TaskId))
            {
                result.ErrorMessage = "Failed to create group task: Task ID was null or empty";
                return result;
            }
            result.TaskCreated = true;

            await _scheduleService.CreateScheduleForTaskAsync(result.TaskId, groupScheduleName, cronExpression, cancellationToken);
            result.ScheduleCreated = true;

            await _webhookService.CreateWebhookForTaskAsync(result.TaskId, webhookUrl, cancellationToken);
            result.WebhookCreated = true;

            _logger.LogInformation("Created new group schedule: Task '{TaskId}', Schedule '{ScheduleName}', Cron '{CronExpression}' with {JournalCount} journals", 
                result.TaskId, groupScheduleName, cronExpression, journalList.Count);

            return result;
        }
        catch (Exception ex)
        {
            result.ErrorMessage = $"Error creating/updating group schedule for cron '{cronExpression}': {ex.Message}";
            _logger.LogError(ex, result.ErrorMessage);
            return result;
        }
    }

    public async Task<ScheduleManagementResult> CreateOrUpdateJournalScheduleAsync(
        JournalScheduleInfo journal, 
        string webhookUrl, 
        CancellationToken cancellationToken = default)
    {
        var result = new ScheduleManagementResult();

        try
        {
            var existingSchedule = await FindExistingScheduleAsync(journal.CronExpression, cancellationToken);
            
            if (existingSchedule != null)
            {
                // Try to update existing task
                result.TaskId = await UpdateExistingTaskAsync(existingSchedule, journal.Url, cancellationToken);
                if (!string.IsNullOrEmpty(result.TaskId))
                {
                    result.ScheduleId = existingSchedule.Id;
                    result.TaskUpdated = true;
                    return result;
                }
            }

            // Create new task, schedule, and webhook
            var taskName = $"vigilit-{ScrapingHelper.SanitizeApifyName(journal.Name)}-{journal.Id}";
            var scheduleName = ScrapingHelper.GenerateUniqueName("schedule", journal.CronExpression);

            result.TaskId = await _taskService.CreateTaskForJournalAsync(journal, taskName, cancellationToken);
            if (string.IsNullOrEmpty(result.TaskId))
            {
                result.ErrorMessage = "Failed to create task: Task ID was null or empty";
                return result;
            }
            result.TaskCreated = true;

            await _scheduleService.CreateScheduleForTaskAsync(result.TaskId, scheduleName, journal.CronExpression, cancellationToken);
            result.ScheduleCreated = true;

            await _webhookService.CreateWebhookForTaskAsync(result.TaskId, webhookUrl, cancellationToken);
            result.WebhookCreated = true;

            return result;
        }
        catch (Exception ex)
        {
            result.ErrorMessage = $"Error creating/updating schedule for journal '{journal.Name}': {ex.Message}";
            _logger.LogError(ex, result.ErrorMessage);
            return result;
        }
    }

    public async Task<string?> UpdateExistingTaskAsync(
        GetListOfSchedulesResponseDataItems schedule, 
        string urlToAdd, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var taskIds = await _taskService.GetTaskIdsByScheduleIdAsync(schedule.Id, cancellationToken);
            if (taskIds?.Count > 0)
            {
                var taskId = taskIds.First();
                await _taskService.UpdateTaskStartUrlsAsync(taskId, schedule.Id, urlToAdd, null, cancellationToken);
                _logger.LogInformation("Updated existing task '{TaskId}' by adding URL '{Url}'", taskId, urlToAdd);
                return taskId;
            }

            _logger.LogWarning("No tasks found for schedule '{ScheduleId}'", schedule.Id);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating existing task for schedule '{ScheduleId}' with URL '{Url}'", 
                schedule.Id, urlToAdd);
            return null;
        }
    }

    private async Task<string?> UpdateExistingTaskWithMultipleUrls(
        GetListOfSchedulesResponseDataItems schedule, 
        IList<JournalScheduleInfo> journals, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var taskIds = await _taskService.GetTaskIdsByScheduleIdAsync(schedule.Id, cancellationToken);
            if (taskIds?.Count > 0)
            {
                var taskId = taskIds.First();
                
                // Add each journal URL to the existing task
                foreach (var journal in journals.Where(j => !string.IsNullOrWhiteSpace(j.Url)))
                {
                    await _taskService.UpdateTaskStartUrlsAsync(taskId, schedule.Id, journal.Url, null, cancellationToken);
                }
                
                _logger.LogInformation("Updated existing task '{TaskId}' by adding {UrlCount} URLs", taskId, journals.Count);
                return taskId;
            }

            _logger.LogWarning("No tasks found for schedule '{ScheduleId}'", schedule.Id);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating existing task for schedule '{ScheduleId}' with multiple URLs", schedule.Id);
            return null;
        }
    }
}
