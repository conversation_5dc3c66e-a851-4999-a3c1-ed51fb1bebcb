using Apify.SDK.Model;
using PharmaLex.VigiLit.Scraping.Client.Models;

namespace PharmaLex.VigiLit.Scraping.Service.Interfaces;

public interface IScheduleManagementService
{
    /// <summary>
    /// Finds an existing schedule with the specified cron expression
    /// </summary>
    /// <param name="cronExpression">The cron expression to search for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The existing schedule if found, null otherwise</returns>
    Task<GetListOfSchedulesResponseDataItems?> FindExistingScheduleAsync(string cronExpression, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates or updates a schedule for a group of journals with the same cron expression
    /// </summary>
    /// <param name="journals">The journals to include in the schedule</param>
    /// <param name="cronExpression">The cron expression for the schedule</param>
    /// <param name="webhookUrl">The webhook URL to use</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing task ID, schedule ID, and webhook information</returns>
    Task<ScheduleManagementResult> CreateOrUpdateGroupScheduleAsync(
        IEnumerable<JournalScheduleInfo> journals, 
        string cronExpression, 
        string webhookUrl, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates or updates a schedule for a single journal
    /// </summary>
    /// <param name="journal">The journal to create/update schedule for</param>
    /// <param name="webhookUrl">The webhook URL to use</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing task ID, schedule ID, and webhook information</returns>
    Task<ScheduleManagementResult> CreateOrUpdateJournalScheduleAsync(
        JournalScheduleInfo journal, 
        string webhookUrl, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing task by adding a URL to it
    /// </summary>
    /// <param name="schedule">The existing schedule</param>
    /// <param name="urlToAdd">The URL to add to the task</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The task ID if successful, null otherwise</returns>
    Task<string?> UpdateExistingTaskAsync(
        GetListOfSchedulesResponseDataItems schedule, 
        string urlToAdd, 
        CancellationToken cancellationToken = default);
}

public class ScheduleManagementResult
{
    public string? TaskId { get; set; }
    public string? ScheduleId { get; set; }
    public bool TaskCreated { get; set; }
    public bool ScheduleCreated { get; set; }
    public bool WebhookCreated { get; set; }
    public bool TaskUpdated { get; set; }
    public string? ErrorMessage { get; set; }
}
